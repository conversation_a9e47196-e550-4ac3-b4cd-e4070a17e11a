// lib/screens/shots_maker_screen.dart
import 'package:flutter/material.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';

class ShotsMakerScreen extends StatelessWidget {
  const ShotsMakerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: Custom3DAppBar(title: "Shots Maker"),
      drawer: AppDrawer(),
      body: Container(
        decoration: BoxDecoration(
          // Realistic 3D white background with depth
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.grey.shade50,
              Colors.grey.shade100,
              Colors.white,
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
          // Add subtle inner shadows for depth
          boxShadow: [
            // Inner shadow effect (top-left light source)
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 20,
              offset: Offset(-5, -5),
              spreadRadius: -10,
            ),
            // Inner shadow effect (bottom-right)
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 15,
              offset: Offset(3, 3),
              spreadRadius: -8,
            ),
          ],
        ),
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Coming Soon Icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.deepPurple.shade400,
                        Colors.purple.shade600,
                        Colors.pink.shade500,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(60),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.purple.withValues(alpha: 0.3),
                        spreadRadius: 0,
                        blurRadius: 20,
                        offset: Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.construction,
                    size: 60,
                    color: Colors.white,
                  ),
                ),

                SizedBox(height: 40),

                // Coming Soon Text
                Text(
                  "Coming Soon!",
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),

                SizedBox(height: 16),

                // Description
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 40),
                  child: Text(
                    "We're working hard to bring you an amazing shots maker feature. Stay tuned for updates!",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                      height: 1.5,
                    ),
                  ),
                ),

                SizedBox(height: 40),

                // Features Preview
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 32),
                  padding: EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.grey.shade200, width: 1),
                  ),
                  child: Column(
                    children: [
                      Text(
                        "Upcoming Features:",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(height: 16),
                      _buildFeatureItem(
                        Icons.edit,
                        "Create Custom Pickup Lines",
                      ),
                      _buildFeatureItem(
                        Icons.palette,
                        "Choose Background Colors",
                      ),
                      _buildFeatureItem(
                        Icons.text_fields,
                        "Custom Fonts & Styles",
                      ),
                      _buildFeatureItem(Icons.share, "Share Your Creations"),
                      _buildFeatureItem(Icons.save, "Save to Gallery"),
                    ],
                  ),
                ),

                SizedBox(height: 40),

                // Notify Button
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 40),
                  child: ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('We\'ll notify you when it\'s ready!'),
                          backgroundColor: Colors.purple.shade600,
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple.shade600,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 32,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      elevation: 8,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.notifications_active, size: 20),
                        SizedBox(width: 8),
                        Text(
                          "Notify Me When Ready",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.purple.shade600),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
            ),
          ),
        ],
      ),
    );
  }
}
