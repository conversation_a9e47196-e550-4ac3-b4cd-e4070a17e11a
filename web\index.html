<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Charm Shots">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Charm Shots</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
      background: #0175C2; /* Consistent with theme color */
    }
    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      transition: opacity 0.5s ease-out;
    }
    .loading-logo {
      width: 120px;
      height: 120px;
      border-radius: 24px;
      box-shadow: 0 8px 24px rgba(0,0,0,0.2);
      animation: pulse 2s infinite ease-in-out;
    }
    .loading-text {
      margin-top: 24px;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 0.5px;
    }
    .no-script-message {
      display: none; /* Hidden by default */
      text-align: center;
      padding: 40px;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.08); }
      100% { transform: scale(1); }
    }
  </style>
</head>
<body>
  <div id="loading-indicator" class="loading-container">
    <img src="icons/Icon-192.png" alt="App Logo" class="loading-logo">
    <p class="loading-text">Loading Charm Shots...</p>
  </div>

  <noscript>
    <style>
      #loading-indicator { display: none; }
      .no-script-message { display: block; }
    </style>
    <div class="no-script-message">
      <h1>JavaScript is required to run this app.</h1>
      <p>Please enable JavaScript in your browser settings and refresh the page.</p>
    </div>
  </noscript>

  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        }
      }).then(function(engineInitializer) {
        return engineInitializer.initializeEngine();
      }).then(function(appRunner) {
        // Hide loading indicator and run the app
        var loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.style.opacity = '0';
          setTimeout(() => loadingIndicator.remove(), 500);
        }
        return appRunner.runApp();
      });
    });
  </script>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
