// This is a basic Flutter widget test for Charm Shots app.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:charm_shots/data/pickup_lines_data.dart';

void main() {
  testWidgets('Charm Shots widget test', (WidgetTester tester) async {
    // Test that our basic widget structure works

    // Create a simple test widget
    await tester.pumpWidget(MaterialApp(home: Scaffold(body: Text('Test'))));

    // Verify that the widget loads
    expect(find.text('Test'), findsOneWidget);
    expect(find.byType(MaterialApp), findsOneWidget);
  });

  test('Pickup lines data structure test', () {
    // Test that our separated data files are working correctly

    // Test English lines
    final englishCategories = PickupLinesData.getAvailableCategories();
    expect(englishCategories.isNotEmpty, true);
    expect(englishCategories.contains('Bold'), true);
    expect(englishCategories.contains('Cute'), true);

    // Test getting lines for a category
    final boldLines = PickupLinesData.getLinesForCategory('Bold', 'English');
    expect(boldLines.isNotEmpty, true);

    // Test Hindi lines
    final hindiLines = PickupLinesData.getLinesForCategory('Bold', 'Hindi');
    expect(hindiLines.isNotEmpty, true);

    // Test supported languages
    final supportedLanguages = PickupLinesData.getSupportedLanguages();
    expect(supportedLanguages.contains('English'), true);
    expect(supportedLanguages.contains('Hindi'), true);
  });
}
