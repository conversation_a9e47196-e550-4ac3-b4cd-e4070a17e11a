// lib/screens/category_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

List<Map<String, dynamic>> getCategoriesWithGenZColors(
  ThemeProvider themeProvider,
) {
  final genZColors = [
    // Muted gradients with shadow tones
    [const Color(0xFFE85A5A), const Color(0xFFD4B85A)], // Muted Coral Sunset
    [const Color(0xFF42B8B1), const Color(0xFF3A8C7A)], // Muted Mint Fresh
    [const Color(0xFF8FCBCF), const Color(0xFFD1B5C4)], // Muted Cotton Candy
    [const Color(0xFF5A6BC7), const Color(0xFF5D4287)], // Muted Purple Rain
    [const Color(0xFFDCC2A3), const Color(0xFFD19B7F)], // Muted Peach Glow
    [const Color(0xFFD67FD8), const Color(0xFFD14658)], // Muted Pink Passion
    [const Color(0xFF4292D4), const Color(0xFF00C5D4)], // Muted Electric Blue
    [const Color(0xFF3AC765), const Color(0xFF32D3B5)], // Muted Neon Green
    [const Color(0xFFD65F87), const Color(0xFFD4C032)], // Muted Sunset Vibes
    [const Color(0xFF5A0FA8), const Color(0xFF2264D4)], // Muted Digital Purple
    [const Color(0xFFD67F82), const Color(0xFFD4A8C7)], // Muted Bubblegum
    [const Color(0xFF7DD3A3), const Color(0xFFD3C465)], // Muted Lime Splash
    [const Color(0xFFD4C587), const Color(0xFFD39387)], // Muted Warm Peach
    [const Color(0xFF639BD4), const Color(0xFF0870B8)], // Muted Ocean Breeze
  ];

  return [
    {
      'name': 'Romantic',
      'iconAsset': 'assets/icons/romantic.svg',
      'gradientColors': genZColors[2],
      'emoji': '💕',
      'description': 'Sweet & charming',
    },
    {
      'name': 'Flirty',
      'iconAsset': 'assets/icons/flirty.svg',
      'gradientColors': genZColors[8],
      'emoji': '😉',
      'description': 'Playful vibes',
    },
    {
      'name': 'Cute',
      'iconAsset': 'assets/icons/cute.svg',
      'gradientColors': genZColors[10],
      'emoji': '🥰',
      'description': 'Adorably sweet',
    },
    {
      'name': 'Funny',
      'iconAsset': 'assets/icons/funny.svg',
      'gradientColors': genZColors[11],
      'emoji': '😂',
      'description': 'LOL moments',
    },
    {
      'name': 'Savage',
      'icon': Icons.whatshot_rounded,
      'gradientColors': genZColors[0],
      'emoji': '🔥',
      'description': 'Straight fire',
    },
    {
      'name': 'Smart',
      'iconAsset': 'assets/icons/cleaver.svg',
      'gradientColors': genZColors[3],
      'emoji': '🧠',
      'description': 'Big brain energy',
    },
    {
      'name': 'Genius',
      'iconAsset': 'assets/icons/genius.svg',
      'gradientColors': genZColors[9],
      'emoji': '⚡',
      'description': 'Next level IQ',
    },
    {
      'name': 'Spicy',
      'iconAsset': 'assets/icons/hookup.svg',
      'gradientColors': genZColors[5],
      'emoji': '🌶️',
      'description': 'Turn up the heat',
    },
    {
      'name': 'Bold',
      'iconAsset': 'assets/icons/bold.svg',
      'gradientColors': genZColors[6],
      'emoji': '💪',
      'description': 'Main character',
    },
    {
      'name': 'Nerdy',
      'iconAsset': 'assets/icons/nerd.svg',
      'gradientColors': genZColors[1],
      'emoji': '🤓',
      'description': 'Geek chic',
    },
    {
      'name': 'Foodie',
      'iconAsset': 'assets/icons/food.svg',
      'gradientColors': genZColors[12],
      'emoji': '🍕',
      'description': 'Snack attack',
    },
    {
      'name': 'Aesthetic',
      'icon': Icons.auto_awesome_rounded,
      'gradientColors': genZColors[13],
      'emoji': '✨',
      'description': 'That\'s the vibe',
    },
  ];
}

class CategoryScreen extends StatelessWidget {
  final String language;
  const CategoryScreen({super.key, required this.language});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final categories = getCategoriesWithGenZColors(themeProvider);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 25,
        shadowColor: Colors.black.withAlpha(38),
        surfaceTintColor: Colors.white,
        scrolledUnderElevation: 30,
        toolbarHeight: 70,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
        ),
        title: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: const Text(
            'Charm Shot',
            style: TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: 22,
              letterSpacing: -0.5,
            ),
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.black),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(8),
          child: Container(
            height: 8,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.black.withAlpha(26), Colors.transparent],
              ),
            ),
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: Container(
        color: Colors.white,
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // Add some top spacing to separate from 3D header
            const SliverToBoxAdapter(child: SizedBox(height: 40)),

            // Categories Grid
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  final category = categories[index];
                  return TweenAnimationBuilder<double>(
                    duration: Duration(milliseconds: 400 + (index * 100)),
                    tween: Tween(begin: 0.0, end: 1.0),
                    curve: Curves.easeOutBack,
                    builder: (context, value, child) {
                      return Transform.translate(
                        offset: Offset(0, 30 * (1 - value)),
                        child: Opacity(
                          opacity: value.clamp(0.0, 1.0),
                          child: child,
                        ),
                      );
                    },
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: GenZCategoryCard(
                        title: category['name'] as String,
                        emoji: category['emoji'] as String,
                        description: category['description'] as String,
                        icon: category['icon'] as IconData?,
                        iconAsset: category['iconAsset'] as String?,
                        gradientColors:
                            category['gradientColors'] as List<Color>,
                        onTap: () => _navigateToLines(
                          context,
                          category['name'] as String,
                          language,
                        ),
                      ),
                    ),
                  );
                }, childCount: categories.length),
              ),
            ),

            // Bottom spacing
            const SliverToBoxAdapter(child: SizedBox(height: 40)),
          ],
        ),
      ),
    );
  }

  void _navigateToLines(
    BuildContext context,
    String categoryName,
    String language,
  ) {
    HapticFeedback.selectionClick();
    Navigator.pushNamed(
      context,
      '/lines',
      arguments: {'category': categoryName, 'language': language},
    );
  }
}

// Gen Z Premium Category Card (unchanged)
class GenZCategoryCard extends StatefulWidget {
  final String title;
  final String emoji;
  final String description;
  final IconData? icon;
  final String? iconAsset;
  final List<Color> gradientColors;
  final VoidCallback onTap;

  const GenZCategoryCard({
    super.key,
    required this.title,
    required this.emoji,
    required this.description,
    this.icon,
    this.iconAsset,
    required this.gradientColors,
    required this.onTap,
  });

  @override
  State<GenZCategoryCard> createState() => _GenZCategoryCardState();
}

class _GenZCategoryCardState extends State<GenZCategoryCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.96).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _shadowAnimation = Tween<double>(begin: 1.0, end: 0.6).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              height: 90,
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [widget.gradientColors[0], widget.gradientColors[1]],
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.gradientColors[0].withAlpha(
                      (255 * 0.3 * _shadowAnimation.value).round(),
                    ),
                    blurRadius: 15 * _shadowAnimation.value,
                    offset: Offset(0, 8 * _shadowAnimation.value),
                  ),
                  BoxShadow(
                    color: Colors.black.withAlpha(
                      (255 * 0.08 * _shadowAnimation.value).round(),
                    ),
                    blurRadius: 12 * _shadowAnimation.value,
                    offset: Offset(0, 4 * _shadowAnimation.value),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(24),
                child: InkWell(
                  borderRadius: BorderRadius.circular(24),
                  onTap: () {
                    HapticFeedback.mediumImpact();
                    widget.onTap();
                  },
                  onTapDown: (_) => _animationController.forward(),
                  onTapUp: (_) => _animationController.reverse(),
                  onTapCancel: () => _animationController.reverse(),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      children: [
                        // Emoji/Icon section
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(64),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withAlpha(77),
                              width: 1.5,
                            ),
                          ),
                          child: Center(
                            child: widget.iconAsset != null
                                ? SvgPicture.asset(
                                    widget.iconAsset!,
                                    width: 26,
                                    height: 26,
                                    colorFilter: const ColorFilter.mode(
                                      Colors.white,
                                      BlendMode.srcIn,
                                    ),
                                  )
                                : widget.icon != null
                                ? Icon(
                                    widget.icon!,
                                    color: Colors.white,
                                    size: 26,
                                  )
                                : Text(
                                    widget.emoji,
                                    style: const TextStyle(fontSize: 24),
                                  ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Text content
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                widget.title,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.white,
                                  letterSpacing: -0.3,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                widget.description,
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white.withAlpha(204),
                                  letterSpacing: 0.2,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Arrow with glow effect
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(51),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.white.withAlpha(77),
                                blurRadius: 8,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.arrow_forward_ios_rounded,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
