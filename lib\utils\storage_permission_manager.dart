// lib/utils/storage_permission_manager.dart
// Utility class for managing storage permissions persistently

import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart'
    as permission_handler;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

class StoragePermissionManager {
  static const String _storagePermissionKey = 'storage_permission_granted';
  static const String _photosPermissionKey = 'photos_permission_granted';

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  static StoragePermissionManager? _instance;
  static StoragePermissionManager get instance {
    _instance ??= StoragePermissionManager._internal();
    return _instance!;
  }

  StoragePermissionManager._internal();

  bool _storagePermissionGranted = false;
  bool _photosPermissionGranted = false;

  /// Initialize permission states from SharedPreferences
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _storagePermissionGranted = prefs.getBool(_storagePermissionKey) ?? false;
      _photosPermissionGranted = prefs.getBool(_photosPermissionKey) ?? false;

      // Verify permissions are still valid
      await _verifyPermissions();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing storage permissions: $e');
      }
    }
  }

  /// Verify that stored permission states are still valid
  Future<void> _verifyPermissions() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = await _deviceInfo.androidInfo;
        final sdkInt = deviceInfo.version.sdkInt;

        if (sdkInt >= 33) { // Android 13+
          final photosStatus = await permission_handler.Permission.photos.status;
          if (photosStatus != permission_handler.PermissionStatus.granted) {
            _photosPermissionGranted = false;
            await _savePhotosPermissionState();
          }
        } else { // Android 12 and below
          final storageStatus =
              await permission_handler.Permission.storage.status;
          if (storageStatus != permission_handler.PermissionStatus.granted) {
            _storagePermissionGranted = false;
            await _saveStoragePermissionState();
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error verifying permissions: $e');
      }
    }
  }

  /// Request storage permissions and save state
  Future<bool> requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = await _deviceInfo.androidInfo;
        final sdkInt = deviceInfo.version.sdkInt;

        if (sdkInt >= 33) { // Android 13+
          if (!_photosPermissionGranted) {
            final photosStatus = await permission_handler.Permission.photos.request();
            _photosPermissionGranted =
                photosStatus == permission_handler.PermissionStatus.granted;
            await _savePhotosPermissionState();
          }
        } else { // Android 12 and below
          if (!_storagePermissionGranted) {
            final storageStatus = await permission_handler.Permission.storage.request();
            _storagePermissionGranted =
                storageStatus == permission_handler.PermissionStatus.granted;
            await _saveStoragePermissionState();
          }
        }
        return _storagePermissionGranted || _photosPermissionGranted;
      } else {
        // iOS doesn't need explicit storage permission for saving to gallery
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error requesting storage permission: $e');
      }
      return false;
    }
  }

  /// Check if storage permission is granted
  Future<bool> hasStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = await _deviceInfo.androidInfo;
        final sdkInt = deviceInfo.version.sdkInt;

        if (sdkInt >= 33) { // Android 13+
          final photosStatus = await permission_handler.Permission.photos.status;
          final hasPhotos = photosStatus == permission_handler.PermissionStatus.granted;
          if (hasPhotos != _photosPermissionGranted) {
            _photosPermissionGranted = hasPhotos;
            await _savePhotosPermissionState();
          }
          return hasPhotos;
        } else { // Android 12 and below
          final storageStatus = await permission_handler.Permission.storage.status;
          final hasStorage = storageStatus == permission_handler.PermissionStatus.granted;
          if (hasStorage != _storagePermissionGranted) {
            _storagePermissionGranted = hasStorage;
            await _saveStoragePermissionState();
          }
          return hasStorage;
        }
      } else {
        return true; // iOS doesn't need explicit permission
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error checking storage permission: $e');
      }
      return false;
    }
  }

  /// Save storage permission state to SharedPreferences
  Future<void> _saveStoragePermissionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_storagePermissionKey, _storagePermissionGranted);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error saving storage permission state: $e');
      }
    }
  }

  /// Save photos permission state to SharedPreferences
  Future<void> _savePhotosPermissionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_photosPermissionKey, _photosPermissionGranted);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error saving photos permission state: $e');
      }
    }
  }

  /// Reset permission states (useful for testing or when user revokes permissions)
  Future<void> resetPermissions() async {
    try {
      _storagePermissionGranted = false;
      _photosPermissionGranted = false;

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storagePermissionKey);
      await prefs.remove(_photosPermissionKey);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error resetting permissions: $e');
      }
    }
  }

  /// Get permission status for debugging
  Map<String, bool> getPermissionStatus() {
    return {
      'storage': _storagePermissionGranted,
      'photos': _photosPermissionGranted,
    };
  }

  /// Check if we should show permission rationale
  Future<bool> shouldShowPermissionRationale() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = await _deviceInfo.androidInfo;
        final sdkInt = deviceInfo.version.sdkInt;

        if (sdkInt >= 33) { // Android 13+
          return await permission_handler.Permission.photos.shouldShowRequestRationale;
        } else { // Android 12 and below
          return await permission_handler.Permission.storage.shouldShowRequestRationale;
        }
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error checking permission rationale: $e');
      }
      return false;
    }
  }

  /// Open app settings for manual permission grant
  Future<void> openAppSettings() async {
    try {
      await permission_handler.openAppSettings();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error opening app settings: $e');
      }
    }
  }
}
