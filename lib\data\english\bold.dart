// lib/data/english/bold.dart
// Bold pickup lines in English

class BoldEnglishLines {
  static const List<String> lines = [
"Is it hot in here, or is it just you?",
"I’m not a photographer, but I can definitely picture us together.",
"Do you believe in love at first sight—or should I walk by again?",
"If looks could kill, you’d definitely be a weapon of mass destruction.",
"Are you a magician? Because whenever I look at you, everyone else disappears.",
"I hope you know CPR, because you just took my breath away.",
"If beauty were a crime, you’d be serving a life sentence.",
"Are you a parking ticket? Because you’ve got ‘fine’ written all over you.",
"Do you have a map? Because I keep getting lost in your eyes.",
"If I said you had a beautiful body, would you hold it against me?",
"I must be a snowflake, because I’ve fallen for you.",
"Do you have a Band-Aid? Because I just scraped my knee falling for you.",
"Are you made of copper and tellurium? Because you’re Cu-Te.",
"If you were a vegetable, you’d be a cute-cumber.",
"Can I follow you home? Cause my parents always told me to follow my dreams.",
"I’m not a genie, but I can make your wishes come true.",
"Are you an interior decorator? Because when I saw you, the entire room became beautiful.",
"Do you have a sunburn, or are you always this hot?",
"If you were a triangle, you’d be acute one.",
"Are you French? Because Eiffel for you.",
"Are you Wi-Fi? Because I’m feeling a connection.",
"Is your name Google? Because you’ve got everything I’ve been searching for.",
"Are you a time traveler? Because I can see you in my future.",
"Is your dad a boxer? Because you’re a knockout!",
"Are you made of stardust? Because your beauty is out of this world.",
"Do you have a pencil? Because I want to erase your past and write our future.",
"If you were a fruit, you’d be a fineapple.",
"Are you a campfire? Because you’re hot and I want s’more.",
"Are you a loan from a bank? Because you’ve got my interest!",
"You must be tired—because you’ve been running through my mind all day.",
  ];
}
