import 'package:go_router/go_router.dart';
import 'package:charm_shots/screens/language_selection_screen.dart';
import 'package:charm_shots/screens/category_screen.dart';
import 'package:charm_shots/screens/lines_list_screen.dart';
import 'package:charm_shots/screens/favorites_screen.dart';
import 'package:charm_shots/screens/performance_debug_screen.dart';

final GoRouter router = GoRouter(
  initialLocation: '/language',
  routes: [
    GoRoute(
      path: '/language',
      builder: (context, state) => const LanguageSelectScreen(),
    ),
    GoRoute(
      path: '/categories/:language',
      builder: (context, state) {
        final language = state.pathParameters['language']!;
        return CategoryScreen(language: language);
      },
    ),
    GoRoute(
      path: '/lines/:language/:category',
      builder: (context, state) {
        final language = state.pathParameters['language']!;
        final category = state.pathParameters['category']!;
        return LinesListScreen(language: language, category: category);
      },
    ),
    GoRoute(
      path: '/favorites',
      builder: (context, state) => const FavoritesScreen(),
    ),
    GoRoute(
      path: '/performance-debug',
      builder: (context, state) => const PerformanceDebugScreen(),
    ),
  ],
);
