// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui';

import 'utils/router.dart';
import 'widgets/exit_dialog.dart';
import 'utils/storage_permission_manager.dart';
import 'providers/theme_provider.dart';
import 'providers/favorites_provider.dart';
import 'utils/image_cache_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (!kDebugMode) {
    debugPrint = (String? message, {int? wrapWidth}) {};
  }

  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => FavoritesProvider()),
      ],
      child: const CharmShotsApp(),
    ),
  );
}

class CharmShotsApp extends StatefulWidget {
  const CharmShotsApp({super.key});

  @override
  State<CharmShotsApp> createState() => _CharmShotsAppState();
}

class _CharmShotsAppState extends State<CharmShotsApp>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeMinimal();
    });

    Future.microtask(() => _initializeHeavyTasks());
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _initializeMinimal() {
    try {
      context.read<FavoritesProvider>().initialize();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing favorites: $e');
      }
    }
  }

  void _initializeHeavyTasks() async {
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    try {
      StoragePermissionManager.instance.initialize();
      await OptimizedImageCache().preloadBackgroundImages();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing storage or preloading images: $e');
      }
    }
  }

  void _refreshMinimal() {
    if (mounted) {
      try {
        context.read<FavoritesProvider>().initialize();
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Error during minimal refresh: $e');
        }
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
        _clearTemporaryCaches();
        break;
      case AppLifecycleState.resumed:
        _refreshMinimal();
        break;
      default:
        break;
    }
  }

  void _clearTemporaryCaches() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp.router(
          title: 'Charm Shots',
          debugShowCheckedModeBanner: false,
          theme: themeProvider.currentTheme,
          routerConfig: router,
          builder: (context, child) {
            return PopScope(
              canPop: false,
              onPopInvoked: (didPop) async {
                if (didPop) return;
                final NavigatorState navigator = Navigator.of(context);
                if (navigator.canPop()) {
                  navigator.pop();
                  return;
                }
                final bool shouldPop = await showExitConfirmationDialog(
                  context,
                );
                if (shouldPop) {
                  SystemNavigator.pop();
                }
              },
              child: MediaQuery(
                data: MediaQuery.of(
                  context,
                ).copyWith(textScaler: const TextScaler.linear(1.0)),
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: child ?? const SizedBox.shrink(),
                ),
              ),
            );
          },
          scrollBehavior: const CustomScrollBehavior(),
        );
      },
    );
  }
}

class CustomScrollBehavior extends ScrollBehavior {
  const CustomScrollBehavior();

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    switch (getPlatform(context)) {
      case TargetPlatform.iOS:
        return const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        );
      default:
        return const ClampingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        );
    }
  }

  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (Theme.of(context).platform == TargetPlatform.android ||
        Theme.of(context).platform == TargetPlatform.iOS) {
      return child;
    }
    return super.buildScrollbar(context, child, details);
  }

  @override
  Widget buildOverscrollIndicator(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    switch (getPlatform(context)) {
      case TargetPlatform.iOS:
        return child;
      default:
        return GlowingOverscrollIndicator(
          axisDirection: details.direction,
          color: Theme.of(context).colorScheme.secondary.withAlpha(25),
          child: child,
        );
    }
  }

  @override
  Set<PointerDeviceKind> get dragDevices => {
    PointerDeviceKind.touch,
    PointerDeviceKind.mouse,
    PointerDeviceKind.stylus,
    PointerDeviceKind.trackpad,
  };
}
