// lib/data/categories.dart
import 'package:flutter/material.dart';
import '../providers/theme_provider.dart';

// Muted Gen Z color palettes with shadow tones for 2025
List<Map<String, dynamic>> getCategoriesWithGenZColors(
  ThemeProvider themeProvider,
) {
  final genZColors = [
    // Muted gradients with shadow tones
    [const Color(0xFFE85A5A), const Color(0xFFD4B85A)], // Muted Coral Sunset
    [const Color(0xFF42B8B1), const Color(0xFF3A8C7A)], // Muted Mint Fresh
    [const Color(0xFF8FCBCF), const Color(0xFFD1B5C4)], // Muted Cotton Candy
    [const Color(0xFF5A6BC7), const Color(0xFF5D4287)], // Muted Purple Rain
    [const Color(0xFFDCC2A3), const Color(0xFFD19B7F)], // Muted Peach Glow
    [const Color(0xFFD67FD8), const Color(0xFFD14658)], // Muted Pink Passion
    [const Color(0xFF4292D4), const Color(0xFF00C5D4)], // Muted Electric Blue
    [const Color(0xFF3AC765), const Color(0xFF32D3B5)], // Muted Neon Green
    [const Color(0xFFD65F87), const Color(0xFFD4C032)], // Muted Sunset Vibes
    [const Color(0xFF5A0FA8), const Color(0xFF2264D4)], // Muted Digital Purple
    [const Color(0xFFD67F82), const Color(0xFFD4A8C7)], // Muted Bubblegum
    [const Color(0xFF7DD3A3), const Color(0xFFD3C465)], // Muted Lime Splash
    [const Color(0xFFD4C587), const Color(0xFFD39387)], // Muted Warm Peach
    [const Color(0xFF639BD4), const Color(0xFF0870B8)], // Muted Ocean Breeze
  ];

  return [
    {
      'name': 'Romantic',
      'iconAsset': 'assets/icons/romantic.svg',
      'gradientColors': genZColors[2],
      'emoji': '💕',
      'description': 'Sweet & charming',
    },
    {
      'name': 'Flirty',
      'iconAsset': 'assets/icons/flirty.svg',
      'gradientColors': genZColors[8],
      'emoji': '😉',
      'description': 'Playful vibes',
    },
    {
      'name': 'Cute',
      'iconAsset': 'assets/icons/cute.svg',
      'gradientColors': genZColors[10],
      'emoji': '🥰',
      'description': 'Adorably sweet',
    },
    {
      'name': 'Funny',
      'iconAsset': 'assets/icons/funny.svg',
      'gradientColors': genZColors[11],
      'emoji': '😂',
      'description': 'LOL moments',
    },
    {
      'name': 'Savage',
      'icon': Icons.whatshot_rounded,
      'gradientColors': genZColors[0],
      'emoji': '🔥',
      'description': 'Straight fire',
    },
    {
      'name': 'Smart',
      'iconAsset': 'assets/icons/cleaver.svg',
      'gradientColors': genZColors[3],
      'emoji': '🧠',
      'description': 'Big brain energy',
    },
    {
      'name': 'Genius',
      'iconAsset': 'assets/icons/genius.svg',
      'gradientColors': genZColors[9],
      'emoji': '⚡',
      'description': 'Next level IQ',
    },
    {
      'name': 'Spicy',
      'iconAsset': 'assets/icons/hookup.svg',
      'gradientColors': genZColors[5],
      'emoji': '🌶️',
      'description': 'Turn up the heat',
    },
    {
      'name': 'Bold',
      'iconAsset': 'assets/icons/bold.svg',
      'gradientColors': genZColors[6],
      'emoji': '💪',
      'description': 'Main character',
    },
    {
      'name': 'Nerdy',
      'iconAsset': 'assets/icons/nerd.svg',
      'gradientColors': genZColors[1],
      'emoji': '🤓',
      'description': 'Geek chic',
    },
    {
      'name': 'Foodie',
      'iconAsset': 'assets/icons/food.svg',
      'gradientColors': genZColors[12],
      'emoji': '🍕',
      'description': 'Snack attack',
    },
    {
      'name': 'Aesthetic',
      'icon': Icons.auto_awesome_rounded,
      'gradientColors': genZColors[13],
      'emoji': '✨',
      'description': 'That\'s the vibe',
    },
  ];
}
